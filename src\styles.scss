@import 'https://at.alicdn.com/t/c/font_2782026_asmbbavkwor.css';

//此文件添加全局的样式
@import './variables.scss';
@import './scss/table.scss';
@import './scss/step.scss';
@import './scss/input.scss';
@import './scss/modal.scss';
@import './scss/message.scss';
@import './scss/scroll.scss';
@import './scss/drawer.scss';
@import './scss/button.scss';
@import './scss/global.scss';
@import './scss/tabs.scss';

.modal-outer {
  .ant-modal-content {
    border-radius: 8px;
  }
}

:root {
  --PrintSizeValue: portrait;
}

@media print {
  @page {
    size: var(--PrintSizeValue) !important;
  }
}

// 全局修复：日期选择器弹出层定位问题
.ant-picker-dropdown {
  position: fixed !important;
  z-index: 9999 !important;

  // 重置可能影响定位的transform
  transform: none !important;

  // 确保弹出层动画不影响定位
  &.ant-slide-up-enter,
  &.ant-slide-up-appear,
  &.ant-slide-up-leave {
    transform: none !important;
  }

  // 确保面板容器正常显示
  .ant-picker-panel-container {
    transform: none !important;
  }
}
